// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xotapvuospvnlhuqyyjp.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhvdGFwdnVvc3B2bmxodXF5eWpwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5OTI0MjAsImV4cCI6MjA2MzU2ODQyMH0.kKf-fSAik4y9sSU61nRXidNnVkv9KbXafMBP4zb0NTY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);